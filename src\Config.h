#ifndef CONFIG_H
#define CONFIG_H

// Debug Configuration
// Set to 1 to enable debug output, 0 to disable for production
#define DEBUG_ENABLED 1

// 总是上报
#define ALWAYS_REPORT 1

// 串口
#define ENABLE_SERIAL_DEBUG 1
#if ENABLE_SERIAL_DEBUG
#define DEBUG_PRINT(x) Serial.print(x)
#define DEBUG_PRINTLN(x) Serial.println(x)
#define DEBUG_PRINTF(fmt, ...) Serial.printf(fmt, ##__VA_ARGS__)
#define DEBUG_BEGIN(baud) Serial.begin(baud)
#define DEBUG_FLASH() Serial.flush()
#define DEBUG_WAIT() while (!Serial && millis() < 5000)
#else
#define DEBUG_PRINT(x)
#define DEBUG_PRINTLN(x)
#define DEBUG_PRINTF(fmt, ...)
#define DEBUG_BEGIN(baud)
#define DEBUG_FLASH()
#define DEBUG_WAIT()
#endif

// 上报数据包含时间字符串
#define REPORT_TIME_STR 1

// MQTT Configuration
#if 1
#define MQTT_SERVER "192.168.10.67"
#define MQTT_PORT 1883
#else
#define MQTT_SERVER "qe13a680.ala.cn-hangzhou.emqxsl.cn"
#define MQTT_PORT 8883
#endif

// Sensor Configuration
#define SENSOR_RX_PIN 8
#define SENSOR_TX_PIN 9
#define SENSOR_BAUDRATE 9600

// Power Management Configuration
#define DETECTION_INTERVAL_SECONDS 20*60

// Reporting Thresholds
#define TDS_THRESHOLD_PPM 10 // TDS difference threshold in ppm
#define TEMP_THRESHOLD_C 0.5 // Temperature difference threshold in Celsius

// Periodic Reporting Configuration
#define PERIODIC_REPORT_INTERVAL_SECONDS (8 * 60 * 60)  // Periodic report interval in seconds (default: 24 hours)
#define SLEEP_MIN_SECONDS 10   // Minimum sleep time in seconds
#define SLEEP_MAX_SECONDS 15   // Maximum sleep time in seconds

// Connection Timeouts
#define WIFI_TIMEOUT_MS 30000 // WiFi connection timeout
#define MQTT_TIMEOUT_MS 10000 // MQTT connection timeout

#define SERIAL_BAUDRATE 115200

// Device Configuration
#define DEVICE_NAME "FishTankSensor"
#define FIRMWARE_VERSION "1.0.0"

// Timing Configuration
#define SENSOR_WARMUP_DELAY_MS 1000

// Memory Management
#define ENABLE_MEMORY_MONITORING false

#endif // CONFIG_H
