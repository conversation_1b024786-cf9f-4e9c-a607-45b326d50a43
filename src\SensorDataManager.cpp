#include "SensorDataManager.h"
#include "PowerManager.h"
#include <esp_random.h>

// Static constants
const char* SensorDataManager::NVS_NAMESPACE = "sensor_data";
const char* SensorDataManager::KEY_LAST_TDS = "last_tds";
const char* SensorDataManager::KEY_LAST_TEMP = "last_temp";
const char* SensorDataManager::KEY_LAST_TIMESTAMP = "last_ts";
const char* SensorDataManager::KEY_LAST_REPORT_TIME = "last_report";
const char* SensorDataManager::KEY_LAST_PERIODIC_REPORT = "last_periodic";
const char* SensorDataManager::KEY_NEXT_PERIODIC_REPORT = "next_periodic";
const char* SensorDataManager::KEY_BOOT_COUNT = "boot_count";
const float SensorDataManager::TEMP_THRESHOLD = TEMP_THRESHOLD_C; // °C from Config.h

SensorDataManager::SensorDataManager() : initialized(false), powerManager(nullptr) {
}

SensorDataManager::~SensorDataManager() {
    if (initialized) {
        preferences.end();
    }
}

void SensorDataManager::setPowerManager(PowerManager* pm) {
    powerManager = pm;
}

bool SensorDataManager::begin() {
    if (!preferences.begin(NVS_NAMESPACE, false)) {
        DEBUG_PRINTLN("Failed to initialize NVS");
        return false;
    }
    
    initialized = true;
    loadState();
    
    // Increment boot count
    incrementBootCount();
    
    DEBUG_PRINTF("SensorDataManager initialized. Boot count: %u\n", state.bootCount);
    
    return true;
}

void SensorDataManager::loadState() {
    state.lastReading.tds = preferences.getInt(KEY_LAST_TDS, 0);
    state.lastReading.temperature = preferences.getFloat(KEY_LAST_TEMP, 0.0);
    state.lastReading.timestamp = preferences.getULong64(KEY_LAST_TIMESTAMP, 0);
    state.lastReading.valid = (state.lastReading.timestamp > 0);
    
    state.lastReportTime = preferences.getULong64(KEY_LAST_REPORT_TIME, 0);
    state.lastPeriodicReportTime = preferences.getULong64(KEY_LAST_PERIODIC_REPORT, 0);
    state.nextPeriodicReportTime = preferences.getULong64(KEY_NEXT_PERIODIC_REPORT, 0);
    state.bootCount = preferences.getUInt(KEY_BOOT_COUNT, 0);

    // If no next periodic report time is set, generate one
    if (state.nextPeriodicReportTime == 0) {
        generateNextPeriodicReportTime();
    }
}

void SensorDataManager::saveState() {
    if (!initialized) return;
    
    preferences.putInt(KEY_LAST_TDS, state.lastReading.tds);
    preferences.putFloat(KEY_LAST_TEMP, state.lastReading.temperature);
    preferences.putULong64(KEY_LAST_TIMESTAMP, state.lastReading.timestamp);
    preferences.putULong64(KEY_LAST_REPORT_TIME, state.lastReportTime);
    preferences.putULong64(KEY_LAST_PERIODIC_REPORT, state.lastPeriodicReportTime);
    preferences.putULong64(KEY_NEXT_PERIODIC_REPORT, state.nextPeriodicReportTime);
    preferences.putUInt(KEY_BOOT_COUNT, state.bootCount);
}

void SensorDataManager::storeReading(const SensorReading& reading) {
    if (!reading.valid) return;
    
    state.lastReading = reading;
    saveState();
    
    DEBUG_PRINTF("Stored reading: TDS=%d ppm, Temp=%.2f°C, Time=%lu\n", 
                  reading.tds, reading.temperature, reading.timestamp);
}

SensorReading SensorDataManager::getLastReading() {
    return state.lastReading;
}

bool SensorDataManager::shouldReportByThreshold(const SensorReading& currentReading) {
    if (!currentReading.valid || !state.lastReading.valid) {
        return true; // Report first reading
    }
    
    int tdsDiff = abs(currentReading.tds - state.lastReading.tds);
    float tempDiff = abs(currentReading.temperature - state.lastReading.temperature);
    
    bool shouldReport = (tdsDiff >= TDS_THRESHOLD) || (tempDiff >= TEMP_THRESHOLD);
    
    if (shouldReport) {
        DEBUG_PRINTF("Threshold exceeded - TDS diff: %d ppm (threshold: %d), Temp diff: %.2f°C (threshold: %.2f)\n",
                      tdsDiff, TDS_THRESHOLD, tempDiff, TEMP_THRESHOLD);
    }
    
    return shouldReport;
}

bool SensorDataManager::shouldReportPeriodically() {
    time_t currentTime = time(nullptr);
    time_t nextReportTime = state.nextPeriodicReportTime; // Already in seconds

    // Check if it's time for periodic report
    if (currentTime >= nextReportTime) {
        DEBUG_PRINTLN("Periodic report time reached");
        return true;
    }

    return false;
}

void SensorDataManager::updateReportState(bool isPeriodicReport) {
    time_t currentTime = time(nullptr);
    state.lastReportTime = currentTime; // Store as seconds

    if (isPeriodicReport) {
        state.lastPeriodicReportTime = currentTime;
        generateNextPeriodicReportTime();
        DEBUG_PRINTF("Periodic report completed. Next periodic report at: %lu\n", state.nextPeriodicReportTime);
    }

    saveState();
}

ReportingState SensorDataManager::getReportingState() {
    return state;
}

void SensorDataManager::generateNextPeriodicReportTime() {
    // Use absolute Unix timestamp for next periodic report time
    time_t currentTime = time(nullptr);

    // Generate random time within the configured interval
    uint32_t randomOffsetSeconds = esp_random() % PERIODIC_REPORT_INTERVAL;
    time_t nextReportTime = currentTime + randomOffsetSeconds;

    // Store the timestamp
    state.nextPeriodicReportTime = nextReportTime;

    // Convert to hours and minutes for logging
    uint32_t hoursFromNow = randomOffsetSeconds / 3600;
    uint32_t minutesFromNow = (randomOffsetSeconds % 3600) / 60;

    DEBUG_PRINTF("Current time: %ld, offset: %u seconds\n", (long)currentTime, randomOffsetSeconds);
    DEBUG_PRINTF("Next periodic report scheduled at %ld, in %u hours and %u minutes\n", (long)nextReportTime, hoursFromNow, minutesFromNow);
    DEBUG_PRINTF("Stored value: %ld\n", (long)state.nextPeriodicReportTime);
}

uint32_t SensorDataManager::getBootCount() {
    return state.bootCount;
}

time_t SensorDataManager::getNextPeriodicReportTimestamp() {
    DEBUG_PRINTF("Getting next periodic report timestamp: %ld\n", (long)state.nextPeriodicReportTime);
    return state.nextPeriodicReportTime;
}

uint64_t SensorDataManager::getCurrentTimestamp() {
    if (powerManager) {
        return powerManager->getTotalUptime(); // Now returns seconds
    } else {
        // Fallback to seconds since boot if power manager is not set
        return millis() / 1000;
    }
}

void SensorDataManager::incrementBootCount() {
    state.bootCount++;
    if (initialized) {
        preferences.putUInt(KEY_BOOT_COUNT, state.bootCount);
    }
}

void SensorDataManager::clearAllData() {
    if (!initialized) return;

    preferences.clear();
    state = ReportingState();
    DEBUG_PRINTLN("All sensor data cleared");
}

void SensorDataManager::forceRegenerateNextPeriodicReport() {
    DEBUG_PRINTLN("Force regenerating next periodic report time...");
    generateNextPeriodicReportTime();
    saveState();
    DEBUG_PRINTF("New next periodic report timestamp: %ld\n", (long)state.nextPeriodicReportTime);
}
