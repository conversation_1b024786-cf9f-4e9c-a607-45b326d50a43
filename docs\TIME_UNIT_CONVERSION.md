# 时间单位转换：毫秒改为秒

## 📋 转换概述

将系统中大部分时间单位从毫秒改为秒，保留必须使用毫秒的ESP32 API调用。

## 🔧 主要更改

### 1. PowerManager 时间单位更改

**RTC内存变量**:
```cpp
// 之前
RTC_DATA_ATTR uint64_t g_totalUptimeMs = 0;

// 现在
RTC_DATA_ATTR uint64_t g_totalUptimeSeconds = 0;
RTC_DATA_ATTR uint64_t g_lastSleepTimeMs = 0; // 保留ms，用于millis()兼容性
RTC_DATA_ATTR uint64_t g_lastSleepDurationUs = 0; // 保留us，用于ESP32 API
```

**getTotalUptime()方法**:
```cpp
// 之前：返回毫秒
return g_totalUptimeMs + millis();

// 现在：返回秒
return g_totalUptimeSeconds + (millis() / 1000);
```

**updateTotalUptime()方法**:
```cpp
// 之前：以毫秒为单位累加
g_totalUptimeMs += (sleepDurationUs / 1000);

// 现在：以秒为单位累加
g_totalUptimeSeconds += (sleepDurationUs / 1000000);
```

### 2. SensorDataManager 时间单位更改

**每日报告间隔**:
```cpp
// 之前
static const unsigned long DAILY_REPORT_INTERVAL = 24UL * 60UL * 60UL * 1000UL; // 毫秒

// 现在
static const unsigned long DAILY_REPORT_INTERVAL = 24UL * 60UL * 60UL; // 秒
```

**时间戳存储**:
```cpp
// 之前：存储为毫秒
state.lastReportTime = currentTime * 1000UL;

// 现在：存储为秒
state.lastReportTime = currentTime;
```

**getCurrentTimestamp()方法**:
```cpp
// 之前：返回毫秒
return millis();

// 现在：返回秒
return millis() / 1000;
```

### 3. ConnectivityManager MQTT消息更改

**uptime字段**:
```cpp
// 之前
doc["uptime"] = millis();

// 现在
doc["uptime_seconds"] = millis() / 1000;
```

### 4. main.cpp 调试输出更改

**系统状态显示**:
```cpp
// 之前
DEBUG_PRINTF("Current session uptime: %lu ms\n", millis());
DEBUG_PRINTF("Total uptime (including deep sleep): %llu ms\n", powerManager.getTotalUptime());

// 现在
DEBUG_PRINTF("Current session uptime: %lu seconds\n", millis() / 1000);
DEBUG_PRINTF("Total uptime (including deep sleep): %llu seconds\n", powerManager.getTotalUptime());
```

## 🔒 保留毫秒的地方

### 1. ESP32 API要求
- `esp_sleep_enable_timer_wakeup()` - 需要微秒
- `millis()` - 系统函数，返回毫秒
- `delay()` - 需要毫秒

### 2. 网络超时
- WiFi连接超时 (`timeoutMs`)
- MQTT连接超时 (`timeoutMs`)
- 这些保持毫秒以提供精确的超时控制

### 3. 传感器预热
- `SENSOR_WARMUP_DELAY_MS` - 保持毫秒精度

### 4. 内部计算
- `g_lastSleepTimeMs` - 用于与`millis()`比较
- `g_lastSleepDurationUs` - ESP32 sleep API需要微秒

## 📊 MQTT消息格式变化

### 之前的格式
```json
{
  "device_id": "fish-tank-sensor-1-xxxx",
  "timestamp": 1703123456,
  "uptime": 12345,
  "tds": 450,
  "temperature": 24.5
}
```

### 现在的格式
```json
{
  "device_id": "fish-tank-sensor-1-xxxx",
  "timestamp": 1703123456,
  "uptime_seconds": 12,
  "tds": 450,
  "temperature": 24.5
}
```

## 🎯 优势

### 1. **可读性提升**
- 调试输出更容易理解
- 时间值更符合人类直觉

### 2. **存储效率**
- 减少大数值的存储需求
- NVS存储更高效

### 3. **计算简化**
- 减少毫秒到秒的转换
- 时间比较更直观

### 4. **网络传输优化**
- MQTT消息中的数值更小
- JSON序列化更高效

## ⚠️ 注意事项

### 1. **精度损失**
- 秒级精度可能不适合某些应用
- 短时间间隔可能被舍入

### 2. **兼容性**
- 外部监控系统需要适应新的字段名
- `uptime` → `uptime_seconds`

### 3. **调试**
- 确保所有时间比较使用相同单位
- 检查时间戳的一致性

## 🔧 调试输出示例

### 系统状态（新格式）
```
--- System Status ---
Current reading: TDS=450 ppm, Temp=24.5°C
Beijing time: 2023-12-21 10:30:56 (timestamp: 1703123456)
Free heap: 234567 bytes
Current session uptime: 12 seconds
Total uptime (including deep sleep): 3600 seconds
```

### PowerManager（新格式）
```
Updated total uptime after sleep: 3600 seconds
Current uptime: 3612 seconds, entering deep sleep for 15 seconds...
```

### 每日报告（新格式）
```
Next daily report scheduled at 1703209856, in 23 hours and 45 minutes
Daily report completed. Next daily report at: 1703296256
```

## 📁 相关文件修改

- `src/PowerManager.h`: 更新RTC变量声明和注释
- `src/PowerManager.cpp`: 修改时间计算逻辑
- `src/SensorDataManager.h`: 更新时间间隔常量
- `src/SensorDataManager.cpp`: 修改时间戳处理
- `src/ConnectivityManager.cpp`: 更新MQTT消息字段
- `src/main.cpp`: 修改调试输出格式
- 新增: `TIME_UNIT_CONVERSION.md` (本文档)

这些更改使系统的时间处理更加一致和直观，同时保持了必要的毫秒精度。
