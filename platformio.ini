; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
platform = espressif32
board = esp32-c3-devkitm-1
framework = arduino
lib_deps =
    knolleary/PubSubClient@^2.8
    bblanchon/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@^7.0.4
monitor_speed = 115200
upload_protocol = esptool
monitor_filters = esp32_exception_decoder
; 如果需要，取消注释并设置正确的端口
; upload_port = COM3
; monitor_port = COM3
board_build.partitions = partitions.csv
build_flags =
    -DCORE_DEBUG_LEVEL=0
    -Os
    -ffunction-sections
    -fdata-sections
    -Wl,--gc-sections
    -Wl,--strip-all
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DARDUINO_USB_MODE=0
    -DCONFIG_ARDUHAL_LOG_DEFAULT_LEVEL=0
    -DCONFIG_LOG_DEFAULT_LEVEL=0
    -DCONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
    -DCONFIG_BTDM_CTRL_BLE_MAX_CONN=1
    -DCONFIG_BTDM_CTRL_BR_EDR_MAX_ACL_CONN=0
    -DCONFIG_BTDM_CTRL_BR_EDR_MAX_SYNC_CONN=0
