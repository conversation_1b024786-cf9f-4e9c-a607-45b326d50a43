#include "ConnectivityManager.h"
#include "PowerManager.h"
#include "Config.h"

// Static member definitions
const char *ConnectivityManager::BLUFI_POP = "abcd1234";
const char *ConnectivityManager::BLUFI_SERVICE_NAME = "PROV_FishTank_SENSOR";
const char *ConnectivityManager::PREF_NAMESPACE = "connectivity";
const char *ConnectivityManager::PREF_WIFI_FAILURE_COUNT = "wifi_failures";
const char *ConnectivityManager::NTP_SERVER1 = "ntp.aliyun.com";
const char *ConnectivityManager::NTP_SERVER2 = "cn.pool.ntp.org";
const char *ConnectivityManager::NTP_SERVER3 = "pool.ntp.org";
ConnectivityManager *ConnectivityManager::instance = nullptr;

ConnectivityManager::ConnectivityManager(const char *mqttServer, uint16_t mqttPort) : mqttServer(mqttServer),
                                                                                      mqttPort(mqttPort),
                                                                                      mqttClient(wifiClient),
                                                                                      initialized(false),
                                                                                      wifiConnected(false),
                                                                                      mqttConnected(false),
                                                                                      provisioningActive(false),
                                                                                      wifiFailureCount(0),
                                                                                      powerManager(nullptr)
{
    instance = this;
}

ConnectivityManager::~ConnectivityManager()
{
    disconnect();
    preferences.end();
}

bool ConnectivityManager::begin()
{
    // Initialize preferences
    preferences.begin(PREF_NAMESPACE, false);
    loadWiFiFailureCount();

    generateDeviceId();
    setupTopics();
    this->mqttUser = deviceId;
    this->mqttPassword = "123";

    // Setup MQTT client with TLS if port 8883
    if (mqttPort == 8883)
    {
        // Configure secure client for TLS with certificate
        const char *rootCA = R"MQTTCA(
-----BEGIN CERTIFICATE-----
MIIDrzCCApegAwIBAgIQCDvgVpBCRrGhdWrJWZHHSjANBgkqhkiG9w0BAQUFADBh
MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3
d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD
QTAeFw0wNjExMTAwMDAwMDBaFw0zMTExMTAwMDAwMDBaMGExCzAJBgNVBAYTAlVT
MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j
b20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENBMIIBIjANBgkqhkiG
9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4jvhEXLeqKTTo1eqUKKPC3eQyaKl7hLOllsB
CSDMAZOnTjC3U/dDxGkAV53ijSLdhwZAAIEJzs4bg7/fzTtxRuLWZscFs3YnFo97
nh6Vfe63SKMI2tavegw5BmV/Sl0fvBf4q77uKNd0f3p4mVmFaG5cIzJLv07A6Fpt
43C/dxC//AH2hdmoRBBYMql1GNXRor5H4idq9Joz+EkIYIvUX7Q6hL+hqkpMfT7P
T19sdl6gSzeRntwi5m3OFBqOasv+zbMUZBfHWymeMr/y7vrTC0LUq7dBMtoM1O/4
gdW7jVg/tRvoSSiicNoxBN33shbyTApOB6jtSj1etX+jkMOvJwIDAQABo2MwYTAO
BgNVHQ8BAf8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUA95QNVbR
TLtm8KPiGxvDl7I90VUwHwYDVR0jBBgwFoAUA95QNVbRTLtm8KPiGxvDl7I90VUw
DQYJKoZIhvcNAQEFBQADggEBAMucN6pIExIK+t1EnE9SsPTfrgT1eXkIoyQY/Esr
hMAtudXH/vTBH1jLuG2cenTnmCmrEbXjcKChzUyImZOMkXDiqw8cvpOp/2PV5Adg
06O/nVsJ8dWO41P0jmP6P6fbtGbfYmbW0W5BjfIttep3Sp+dWOIrWcBAI+0tKIJF
PnlUkiaY4IBIqDfv8NZ5YBberOgOzW6sRBc4L0na4UU+Krk2U886UAb3LujEV0ls
YSEY1QSteDwsOoBrp+uvFRTp2InBuThs4pFsiv9kuXclVzDAGySj4dzp30d8tbQk
CAUw7C29C79Fv1C5qfPrmAESrciIxpg0X40KPMbp1ZWVbd4=
-----END CERTIFICATE-----
)MQTTCA";

        wifiClientSecure.setCACert(rootCA);
        mqttClient.setClient(wifiClientSecure);
        DEBUG_PRINTLN("TLS enabled for MQTT with CA certificate");
    }

    // Set MQTT buffer size to handle larger messages
    mqttClient.setBufferSize(1024);
    mqttClient.setServer(this->mqttServer.c_str(), this->mqttPort);
    mqttClient.setCallback(mqttCallback);

    DEBUG_PRINTF("MQTT buffer size set to: %d bytes\n", mqttClient.getBufferSize());

    // Setup WiFi event handler
    WiFi.onEvent(bluFiEventHandler);

    initialized = true;

    DEBUG_PRINTF("ConnectivityManager initialized for device: %s\n", deviceId.c_str());
    DEBUG_PRINTF("MQTT Server: %s:%d\n", mqttServer, mqttPort);
    DEBUG_PRINTF("WiFi failure count: %d\n", wifiFailureCount);

    return true;
}

bool ConnectivityManager::startBluFiProvisioning(uint32_t timeoutMs)
{
    if (!initialized)
    {
        DEBUG_PRINTLN("ConnectivityManager not initialized!");
        return false;
    }

    DEBUG_PRINTLN("Starting BluFi provisioning...");
    DEBUG_PRINTF("Service name: %s\n", BLUFI_SERVICE_NAME);
    DEBUG_PRINTF("Proof of possession: %s\n", BLUFI_POP);
    DEBUG_PRINTF("Timeout: %lu ms\n", timeoutMs);

    provisioningActive = true;

    // Generate UUID for BluFi
    uint8_t uuid[16] = {0xb4, 0xdf, 0x5a, 0x1c, 0x3f, 0x6b, 0xf4, 0xbf,
                        0xea, 0x4a, 0x82, 0x03, 0x04, 0x90, 0x1a, 0x02};

    // Start BluFi provisioning
    WiFiProv.beginProvision(
        WIFI_PROV_SCHEME_BLE,
        WIFI_PROV_SCHEME_HANDLER_FREE_BLE,
        WIFI_PROV_SECURITY_1,
        BLUFI_POP,
        BLUFI_SERVICE_NAME,
        NULL, // service_key not needed for BLE
        uuid,
        false // reset_provisioned - don't reset existing credentials
    );

    // Print QR code for easy connection
    WiFiProv.printQR(BLUFI_SERVICE_NAME, BLUFI_POP, "ble");

    // Wait for provisioning to complete or timeout
    uint32_t startTime = millis();
    while (provisioningActive && (millis() - startTime) < timeoutMs)
    {
        delay(100);
        // Check if WiFi is connected (provisioning successful)
        if (WiFi.status() == WL_CONNECTED)
        {
            DEBUG_PRINTLN("BluFi provisioning successful!");
            provisioningActive = false;
            wifiConnected = true;
            resetWiFiFailureCount(); // Reset failure count on successful connection
            return true;
        }
    }

    // Timeout reached
    if (provisioningActive)
    {
        DEBUG_PRINTLN("BluFi provisioning timeout!");
        provisioningActive = false;
        return false;
    }

    return false;
}

bool ConnectivityManager::connectWiFi(uint32_t timeoutMs)
{
    if (wifiConnected)
    {
        return true;
    }

    // Try to connect using stored credentials
    WiFi.mode(WIFI_STA);
    WiFi.begin(); // Use stored credentials

    DEBUG_PRINT("Connecting to WiFi using stored credentials");

    uint32_t startTime = millis();
    while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < timeoutMs)
    {
        delay(WIFI_RETRY_INTERVAL);
        DEBUG_PRINT(".");
    }

    if (WiFi.status() == WL_CONNECTED)
    {
        wifiConnected = true;
        onWiFiConnected();
        resetWiFiFailureCount(); // Reset failure count on successful connection
        return true;
    }
    else
    {
        DEBUG_PRINTLN("\nWiFi connection failed!");
        incrementWiFiFailureCount();
        return false;
    }
}

bool ConnectivityManager::connectMQTT(uint32_t timeoutMs)
{
    if (!wifiConnected)
    {
        DEBUG_PRINTLN("WiFi not connected, cannot connect to MQTT");
        return false;
    }

    if (mqttConnected && mqttClient.connected())
    {
        return true;
    }

    // Check available memory before MQTT connection
    DEBUG_PRINTF("Free heap before MQTT connection: %u bytes\n", ESP.getFreeHeap());

    // Test basic network connectivity
    DEBUG_PRINTF("WiFi status: %d, IP: %s\n", WiFi.status(), WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("Connecting to MQTT broker: %s:%d\n", mqttServer.c_str(), mqttPort);

    uint32_t startTime = millis();
    int attemptCount = 0;
    const int maxAttempts = 5; // Limit number of attempts

    while (!mqttClient.connected() && (millis() - startTime) < timeoutMs && attemptCount < maxAttempts)
    {
        attemptCount++;
        DEBUG_PRINTF("MQTT connection attempt %d...\n", attemptCount);
        bool connected;

        if (mqttUser.length() > 0)
        {
            DEBUG_PRINTF("Connecting with credentials: %s\n", clientId.c_str());
            connected = mqttClient.connect(clientId.c_str(), mqttUser.c_str(), mqttPassword.c_str());
        }
        else
        {
            DEBUG_PRINTF("Connecting without credentials: %s\n", clientId.c_str());
            connected = mqttClient.connect(clientId.c_str());
        }

        if (connected)
        {
            mqttConnected = true;
            DEBUG_PRINTLN("MQTT connected successfully!");

            // Publish connection status
            // publishStatus(0, "connected");

            return true;
        }
        else
        {
            int state = mqttClient.state();
            DEBUG_PRINTF("MQTT connection failed, state: %d\n", state);

            // Print specific error messages
            switch (state)
            {
            case -4:
                DEBUG_PRINTLN("Connection timeout");
                break;
            case -3:
                DEBUG_PRINTLN("Connection lost");
                break;
            case -2:
                DEBUG_PRINTLN("Connect failed");
                break;
            case -1:
                DEBUG_PRINTLN("Disconnected");
                break;
            case 1:
                DEBUG_PRINTLN("Bad protocol");
                break;
            case 2:
                DEBUG_PRINTLN("Bad client ID");
                break;
            case 3:
                DEBUG_PRINTLN("Unavailable");
                break;
            case 4:
                DEBUG_PRINTLN("Bad credentials");
                break;
            case 5:
                DEBUG_PRINTLN("Unauthorized");
                break;
            default:
                DEBUG_PRINTF("Unknown error: %d\n", state);
                break;
            }
            delay(MQTT_RETRY_INTERVAL);
        }
    }

    DEBUG_PRINTF("\nMQTT connection failed after %d attempts!\n", attemptCount);
    DEBUG_PRINTF("Final MQTT state: %d\n", mqttClient.state());
    DEBUG_PRINTF("Time elapsed: %lu ms\n", millis() - startTime);
    DEBUG_PRINTF("Free heap after failed connection: %u bytes\n", ESP.getFreeHeap());

    // Clean up any partial connection state
    mqttClient.disconnect();
    mqttConnected = false;

    return false;
}

void ConnectivityManager::disconnect()
{
    if (mqttConnected)
    {
        mqttClient.disconnect();
        mqttConnected = false;
    }

    if (wifiConnected)
    {
        WiFi.disconnect(false); // Disconnect but keep stored credentials
        wifiConnected = false;
    }

    DEBUG_PRINTLN("Disconnected from WiFi and MQTT");
}

bool ConnectivityManager::isConnected()
{
    return wifiConnected && mqttConnected && mqttClient.connected();
}

bool ConnectivityManager::publishSensorData(const SensorReading &reading, bool isPeriodicReport, time_t nextPeriodicReportTime)
{
    if (!isConnected())
    {
        DEBUG_PRINTLN("Not connected, cannot publish sensor data");
        return false;
    }

    String payload = createSensorDataPayload(reading, isPeriodicReport, nextPeriodicReportTime);

    bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), true); // retained message

    if (success)
    {
        DEBUG_PRINTF("Published sensor data: %s\n", payload.c_str());
    }
    else
    {
        DEBUG_PRINTF("Failed to publish sensor data: %s\n", payload.c_str());
    }

    return success;
}

bool ConnectivityManager::publishSensorError(const char *errorMessage, time_t nextPeriodicReportTime)
{
    if (!isConnected())
    {
        DEBUG_PRINTLN("Not connected, cannot publish sensor error");
        return false;
    }

    // Create error payload similar to sensor data but with error information
    JsonDocument doc;

    // Use Beijing timestamp
    time_t beijingTime = getBeijingTimestamp();

    doc["device_id"] = deviceId;
    doc["timestamp"] = beijingTime;
    doc["beijing_time"] = getBeijingTimeString();
    doc["error"] = true;
    doc["error_message"] = errorMessage;
    doc["tds"] = nullptr;         // No valid sensor data
    doc["temperature"] = nullptr; // No valid sensor data
    doc["daily_report"] = false;
    doc["uptime_seconds"] = millis() / 1000.0;

    // Add next detection time
    if (powerManager)
    {
        time_t nextDetectionTime = beijingTime + DETECTION_INTERVAL_SECONDS;

        struct tm nextDetectionTm;
        localtime_r(&nextDetectionTime, &nextDetectionTm);
        char nextDetectionStr[64];
        strftime(nextDetectionStr, sizeof(nextDetectionStr), "%Y-%m-%d %H:%M:%S", &nextDetectionTm);

        doc["next_detection_time"] = nextDetectionStr;
        doc["next_detection_timestamp"] = nextDetectionTime;
    }

    // Add next periodic report time if provided (keep field name for compatibility)
    if (nextPeriodicReportTime > 0)
    {
        struct tm nextDailyTm;
        localtime_r(&nextPeriodicReportTime, &nextDailyTm);
        char nextDailyStr[64];
        strftime(nextDailyStr, sizeof(nextDailyStr), "%Y-%m-%d %H:%M:%S", &nextDailyTm);

        doc["next_daily_report_time"] = nextDailyStr;
        doc["next_daily_report_timestamp"] = nextPeriodicReportTime;
    }

    String payload;
    serializeJson(doc, payload);

    bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), true); // retained message

    if (success)
    {
        DEBUG_PRINTF("Published sensor error: %s\n", payload.c_str());
    }
    else
    {
        DEBUG_PRINTF("Failed to publish sensor error: %s\n", payload.c_str());
    }

    return success;
}

bool ConnectivityManager::publishStatus(uint32_t bootCount, const char *status)
{
    if (!isConnected())
    {
        return false;
    }

    String payload = createStatusPayload(bootCount, status);

    bool success = mqttClient.publish(topicStatus.c_str(), payload.c_str());

    if (success)
    {
        DEBUG_PRINTF("Published status: %s\n", payload.c_str());
    }

    return success;
}

void ConnectivityManager::loop()
{
    if (mqttConnected)
    {
        mqttClient.loop();
    }
}

String ConnectivityManager::getConnectionStatus()
{
    String status = "WiFi: ";
    status += wifiConnected ? "Connected" : "Disconnected";
    status += ", MQTT: ";
    status += mqttConnected ? "Connected" : "Disconnected";
    status += ", Failures: " + String(wifiFailureCount);

    if (wifiConnected)
    {
        status += " (IP: " + WiFi.localIP().toString() + ")";
    }

    return status;
}

bool ConnectivityManager::hasStoredCredentials()
{
    // Ensure WiFi is initialized
    if (WiFi.getMode() == WIFI_OFF)
    {
        WiFi.mode(WIFI_STA);
        delay(100); // Give WiFi time to initialize
    }

    // Check if WiFi credentials are stored in NVS
    wifi_config_t conf;
    esp_err_t err = esp_wifi_get_config(WIFI_IF_STA, &conf);

    DEBUG_PRINTF("WiFi config check: err=%d, SSID='%s'\n", err, (char *)conf.sta.ssid);

    return err == ESP_OK && (strlen((char *)conf.sta.ssid) > 0);
}

void ConnectivityManager::clearStoredCredentials()
{
    DEBUG_PRINTLN("Clearing stored WiFi credentials...");
    WiFi.disconnect(true, true); // Disconnect and erase stored credentials
    resetWiFiFailureCount();
}

uint8_t ConnectivityManager::getWiFiFailureCount()
{
    return wifiFailureCount;
}

void ConnectivityManager::resetWiFiFailureCount()
{
    wifiFailureCount = 0;
    saveWiFiFailureCount();
    DEBUG_PRINTLN("WiFi failure count reset");
}

void ConnectivityManager::incrementWiFiFailureCount()
{
    wifiFailureCount++;
    saveWiFiFailureCount();
    DEBUG_PRINTF("WiFi failure count incremented to: %d\n", wifiFailureCount);
}

void ConnectivityManager::loadWiFiFailureCount()
{
    wifiFailureCount = preferences.getUChar(PREF_WIFI_FAILURE_COUNT, 0);
}

void ConnectivityManager::saveWiFiFailureCount()
{
    preferences.putUChar(PREF_WIFI_FAILURE_COUNT, wifiFailureCount);
}

void ConnectivityManager::generateDeviceId()
{
    uint8_t mac[6];
    WiFi.macAddress(mac);

    deviceId = "fish-tank-sensor-1-";
    for (int i = 0; i < 6; i++)
    {
        if (mac[i] < 16)
            deviceId += "0";
        deviceId += String(mac[i], HEX);
    }

    clientId = deviceId; // + "-" + String(millis());
}

void ConnectivityManager::setupTopics()
{
    String baseTopic = "fishtank/" + deviceId;
    topicSensorData = baseTopic + "/sensor";
    topicStatus = baseTopic + "/status";
    topicHeartbeat = baseTopic + "/heartbeat";
}

void ConnectivityManager::mqttCallback(char *topic, byte *payload, unsigned int length)
{
    // For future use if we need to handle incoming MQTT messages
    DEBUG_PRINTF("MQTT message received on topic: %s\n", topic);
}

String ConnectivityManager::createSensorDataPayload(const SensorReading &reading, bool isPeriodicReport, time_t nextPeriodicReportTime)
{
    JsonDocument doc;

    // Use Beijing timestamp instead of reading.timestamp
    doc["device_id"] = deviceId;
    doc["tds"] = reading.tds;
    doc["temperature"] = reading.temperature;
    doc["daily_report"] = isPeriodicReport; // Keep field name for compatibility
    doc["uptime_seconds"] = millis() / 1000.0;
    time_t beijingTime = getBeijingTimestamp();
    doc["timestamp"] = beijingTime;
#if REPORT_TIME_STR
    doc["beijing_time"] = getBeijingTimeString();
#endif

    // Add next detection time (current time + random sleep duration)
    if (powerManager)
    {
        // Calculate next detection time based on sleep configuration
        time_t nextDetectionTime = beijingTime + DETECTION_INTERVAL_SECONDS;
        doc["next_detection_timestamp"] = nextDetectionTime;

#if REPORT_TIME_STR
        struct tm nextDetectionTm;
        localtime_r(&nextDetectionTime, &nextDetectionTm);
        char nextDetectionStr[64];
        strftime(nextDetectionStr, sizeof(nextDetectionStr), "%Y-%m-%d %H:%M:%S", &nextDetectionTm);

        doc["next_detection_time"] = nextDetectionStr;
#endif
    }

    // Add next periodic report time if provided (keep field name for compatibility)
    if (nextPeriodicReportTime > 0)
    {
        doc["next_daily_report_timestamp"] = nextPeriodicReportTime;

#if REPORT_TIME_STR
        struct tm nextDailyTm;
        localtime_r(&nextPeriodicReportTime, &nextDailyTm);
        char nextDailyStr[64];
        strftime(nextDailyStr, sizeof(nextDailyStr), "%Y-%m-%d %H:%M:%S", &nextDailyTm);
        doc["next_daily_report_time"] = nextDailyStr;
#endif
    }

    String payload;
    serializeJson(doc, payload);
    return payload;
}

String ConnectivityManager::createStatusPayload(uint32_t bootCount, const char *status)
{
    JsonDocument doc;

    doc["device_id"] = deviceId;
    doc["status"] = status;
    doc["boot_count"] = bootCount;
    doc["uptime_seconds"] = millis() / 1000.0;
    doc["free_heap"] = ESP.getFreeHeap();
    doc["wifi_rssi"] = WiFi.RSSI();

    String payload;
    serializeJson(doc, payload);
    return payload;
}

void ConnectivityManager::setPowerManager(PowerManager *pm)
{
    powerManager = pm;
}

bool ConnectivityManager::syncTimeWithNTP()
{
    if (!wifiConnected)
    {
        DEBUG_PRINTLN("WiFi not connected, cannot sync time");
        return false;
    }

    DEBUG_PRINTLN("Syncing time with NTP servers...");

    // Configure NTP
    configTime(GMT_OFFSET_SEC, DAYLIGHT_OFFSET_SEC, NTP_SERVER1, NTP_SERVER2, NTP_SERVER3);

    // Wait for time to be set
    int retry = 0;
    const int maxRetries = 10;
    while (time(nullptr) < 8 * 3600 * 2 && retry < maxRetries)
    {
        delay(1000);
        retry++;
        DEBUG_PRINT(".");
    }

    if (retry >= maxRetries)
    {
        DEBUG_PRINTLN("\nFailed to sync time with NTP");
        return false;
    }

    DEBUG_PRINTLN("\nTime synchronized with NTP");
    DEBUG_PRINTF("Current Beijing time: %s\n", getBeijingTimeString().c_str());
    return true;
}

time_t ConnectivityManager::getBeijingTimestamp()
{
    time_t now = time(nullptr);
    if (now < 8 * 3600 * 2)
    { // If time is not set (less than 2 days since epoch)
        DEBUG_PRINTLN("Warning: Time not synchronized, using system uptime as fallback");
        return millis() / 1000; // Return seconds since boot as fallback
    }
    return now;
}

String ConnectivityManager::getBeijingTimeString()
{
    time_t now = time(nullptr);
    if (now < 8 * 3600 * 2)
    { // If time is not set
        return "Time not synchronized";
    }

    struct tm timeinfo;
    if (!localtime_r(&now, &timeinfo))
    {
        return "Time conversion failed";
    }

    char timeStr[64];
    strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &timeinfo);
    return String(timeStr);
}

void ConnectivityManager::onWiFiConnected()
{
    DEBUG_PRINTLN();
    DEBUG_PRINTF("WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("Signal strength: %d dBm\n", WiFi.RSSI());

    // Sync time with NTP after WiFi connection
    syncTimeWithNTP();
}

void ConnectivityManager::onWiFiDisconnected()
{
    wifiConnected = false;
    mqttConnected = false;
    DEBUG_PRINTLN("WiFi disconnected(onWiFiDisconnected)");
}

void ConnectivityManager::bluFiEventHandler(arduino_event_t *sys_event)
{
    switch (sys_event->event_id)
    {
    case ARDUINO_EVENT_WIFI_STA_GOT_IP:
        DEBUG_PRINT("\nWiFi connected! IP address: ");
        DEBUG_PRINTLN(IPAddress(sys_event->event_info.got_ip.ip_info.ip.addr));
        if (instance)
        {
            instance->wifiConnected = true;
        }
        break;

    case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
        DEBUG_PRINTLN("\nWiFi disconnected(bluFiEventHandler)");
        if (instance)
        {
            instance->wifiConnected = false;
            instance->mqttConnected = false;
        }
        break;

    case ARDUINO_EVENT_PROV_START:
        DEBUG_PRINTLN("\nBluFi provisioning started");
        DEBUG_PRINTLN("Use the ESP BLE Provisioning app to configure WiFi");
        break;

    case ARDUINO_EVENT_PROV_CRED_RECV:
    {
        DEBUG_PRINTLN("\nReceived WiFi credentials via BluFi");
        DEBUG_PRINT("\tSSID: ");
        DEBUG_PRINTLN((const char *)sys_event->event_info.prov_cred_recv.ssid);
        DEBUG_PRINT("\tPassword: ");
        DEBUG_PRINTLN((char const *)sys_event->event_info.prov_cred_recv.password);

        wifi_config_t wifi_config;
        wifi_config.sta = sys_event->event_info.prov_cred_recv;
        esp_err_t err = esp_wifi_set_config(WIFI_IF_STA, &wifi_config);
        DEBUG_PRINTF("\nesp_wifi_set_config: %d", err);
        break;
    }
    case ARDUINO_EVENT_PROV_CRED_FAIL:
        DEBUG_PRINTLN("\nBluFi provisioning failed!");
        if (sys_event->event_info.prov_fail_reason == WIFI_PROV_STA_AUTH_ERROR)
        {
            DEBUG_PRINTLN("WiFi password incorrect");
        }
        else
        {
            DEBUG_PRINTLN("WiFi network not found");
        }
        if (instance)
        {
            instance->provisioningActive = false;
        }
        break;

    case ARDUINO_EVENT_PROV_CRED_SUCCESS:
        DEBUG_PRINTLN("\nBluFi provisioning successful!");
        break;

    case ARDUINO_EVENT_PROV_END:
        DEBUG_PRINTLN("\nBluFi provisioning ended");
        if (instance)
        {
            instance->provisioningActive = false;
        }
        break;

    default:
        break;
    }
}
