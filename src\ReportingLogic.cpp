#include "ReportingLogic.h"

ReportingLogic::ReportingLogic(SensorDataManager &dataManager, ConnectivityManager &connectivity) : dataManager(dataManager),
                                                                                                    connectivity(connectivity),
                                                                                                    initialized(false),
                                                                                                    totalReports(0),
                                                                                                    thresholdReports(0),
                                                                                                    periodicReports(0),
                                                                                                    errorReports(0)
{
}

void ReportingLogic::begin()
{
    initialized = true;
    DEBUG_PRINTLN("ReportingLogic initialized");
}

ReportDecision ReportingLogic::analyzeReading(const SensorReading &currentReading)
{
    ReportDecision decision;

    if (!currentReading.valid)
    {
        decision.shouldReport = true;
        decision.reasons |= REPORT_ERROR;
        decision.reasonText = "Invalid sensor reading";
        return decision;
    }

    // Check if this is the first reading
    SensorReading lastReading = dataManager.getLastReading();
    if (!lastReading.valid)
    {
        decision.shouldReport = true;
        decision.reasons |= REPORT_FIRST_READING;
    }

    // Check threshold-based reporting
    if (dataManager.shouldReportByThreshold(currentReading))
    {
        decision.shouldReport = true;

        if (lastReading.valid)
        {
            int tdsDiff = abs(currentReading.tds - lastReading.tds);
            float tempDiff = abs(currentReading.temperature - lastReading.temperature);

            if (tdsDiff >= 1000)
            {
                decision.reasons |= REPORT_TDS_THRESHOLD;
            }
            if (tempDiff >= 1.0)
            {
                decision.reasons |= REPORT_TEMP_THRESHOLD;
            }
        }
    }

    // Check periodic reporting
    if (dataManager.shouldReportPeriodically())
    {
        decision.shouldReport = true;
        decision.reasons |= REPORT_PERIODIC;
    }

#if ALWAYS_REPORT
    decision.shouldReport = true;
    decision.reasons |= REPORT_ALWAYS_CONFIG;
#endif
    // Build reason text
    decision.reasonText = buildReasonText(decision.reasons);

    return decision;
}

bool ReportingLogic::executeReport(const SensorReading &reading, const ReportDecision &decision)
{
    DEBUG_PRINTF("Executing report: %s\n", decision.reasonText.c_str());

    // Attempt to connect if not already connected
    if (!attemptConnection())
    {
        DEBUG_PRINTLN("Failed to establish connection for reporting");
        logReportAttempt(decision, false);
        return false;
    }

    // Determine if this is a periodic report
    bool isPeriodicReport = (decision.reasons & REPORT_PERIODIC) != 0;

    // Get next periodic report time
    time_t nextPeriodicReportTime = dataManager.getNextPeriodicReportTimestamp();

    bool success;

    // Check if this is an error report
    if (decision.reasons & REPORT_ERROR)
    {
        // Publish sensor error instead of normal sensor data
        success = connectivity.publishSensorError(decision.reasonText.c_str(), nextPeriodicReportTime);
    }
    else
    {
        // Publish normal sensor data
        success = connectivity.publishSensorData(reading, isPeriodicReport, nextPeriodicReportTime);
    }

    if (success)
    {
        // Update reporting state
        dataManager.updateReportState(isPeriodicReport);

        // Update statistics
        totalReports++;
        if (decision.reasons & REPORT_PERIODIC)
            periodicReports++;
        if (decision.reasons & (REPORT_TDS_THRESHOLD | REPORT_TEMP_THRESHOLD))
            thresholdReports++;
        if (decision.reasons & REPORT_ERROR)
            errorReports++;

        DEBUG_PRINTF("Report successful! Total reports: %u\n", totalReports);
    }

    logReportAttempt(decision, success);
    return success;
}

bool ReportingLogic::isPeriodicReportDue()
{
    return dataManager.shouldReportPeriodically();
}

bool ReportingLogic::forceReport(const SensorReading &reading, const char *reason)
{
    ReportDecision decision;
    decision.shouldReport = true;
    decision.reasons = REPORT_NONE; // Custom reason
    decision.reasonText = String("Forced: ") + reason;

    return executeReport(reading, decision);
}

void ReportingLogic::printReportingStats()
{
    DEBUG_PRINTLN("=== Reporting Statistics ===");
    DEBUG_PRINTF("Total reports: %u\n", totalReports);
    DEBUG_PRINTF("Threshold reports: %u\n", thresholdReports);
    DEBUG_PRINTF("Periodic reports: %u\n", periodicReports);
    DEBUG_PRINTF("Error reports: %u\n", errorReports);

    ReportingState state = dataManager.getReportingState();
    DEBUG_PRINTF("Boot count: %u\n", state.bootCount);
    DEBUG_PRINTF("Last report time: %lu\n", state.lastReportTime);
    DEBUG_PRINTF("Next periodic report: %lu\n", state.nextPeriodicReportTime);
    DEBUG_PRINTLN("============================");
}

String ReportingLogic::buildReasonText(uint8_t reasons)
{
    if (reasons == REPORT_NONE)
    {
        return "No report needed";
    }

    String text = "";
    bool first = true;

    if (reasons & REPORT_FIRST_READING)
    {
        if (!first)
            text += ", ";
        text += "First reading";
        first = false;
    }

    if (reasons & REPORT_TDS_THRESHOLD)
    {
        if (!first)
            text += ", ";
        text += "TDS threshold exceeded";
        first = false;
    }

    if (reasons & REPORT_TEMP_THRESHOLD)
    {
        if (!first)
            text += ", ";
        text += "Temperature threshold exceeded";
        first = false;
    }

    if (reasons & REPORT_PERIODIC)
    {
        if (!first)
            text += ", ";
        text += "Periodic report due";
        first = false;
    }

    if (reasons & REPORT_ERROR)
    {
        if (!first)
            text += ", ";
        text += "Error condition";
        first = false;
    }
    if (reasons & REPORT_ALWAYS_CONFIG)
    {
        if (!first)
            text += ", ";
        text += "Always report config";
        first = false;
    }

    return text;
}

bool ReportingLogic::attemptConnection()
{
    // Check if already connected
    if (connectivity.isConnected())
    {
        return true;
    }

    DEBUG_PRINTLN("Attempting to establish connection...");

    // Try to connect to WiFi
    if (!connectivity.connectWiFi(WIFI_TIMEOUT_MS))
    {
        DEBUG_PRINTLN("WiFi connection failed");
        return false;
    }

    // Try to connect to MQTT
    if (!connectivity.connectMQTT(MQTT_TIMEOUT_MS))
    {
        DEBUG_PRINTLN("MQTT connection failed");
        return false;
    }

    DEBUG_PRINTLN("Connection established successfully");
    return true;
}

void ReportingLogic::logReportAttempt(const ReportDecision &decision, bool success)
{
    DEBUG_PRINTF("Report attempt - Reason: %s, Success: %s\n",
                 decision.reasonText.c_str(),
                 success ? "YES" : "NO");

    if (!success)
    {
        DEBUG_PRINTF("Connection status: %s\n", connectivity.getConnectionStatus().c_str());
    }
}
